import bg from "../../assets/customer-auth-bg.png";
import mobileBg from "../../assets/customer-auth-bg-mobile.png";

function CustomerAuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
    <div
      className={`bg-cover h-[100vh] w-[100vw] hidden lg:block`}
      style={{ backgroundImage: `url(${bg})` }}
    >
      {children}
    </div>
    <div
      className={`bg-cover h-[100vh] w-[100vw] lg:hidden block`}
      style={{ backgroundImage: `url(${mobileBg})` }}
    >
      {children}
    </div>
    </>
  );
}

export default CustomerAuthLayout;
